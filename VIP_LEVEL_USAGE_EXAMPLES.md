# VIP 等级系统使用示例

## 1. API 端 VIP 等级列表接口

### 1.1 接口地址

- **分页列表**: `GET /api/user/vip-level/list` （需要登录）

### 1.2 请求参数（分页列表）

```json
{
  "page": 1,
  "page_size": 10,
  "filter": {
    "name": "VIP1",
    "level": 1
  },
  "sort": "sort"
}
```

### 1.3 获取所有数据（不分页）

```json
{
  "page_size": -1
}
```

### 1.4 响应示例

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "data": [
      {
        "id": 1,
        "name": "VIP0",
        "level": 0,
        "icon": "/images/vip0.png",
        "color": "#999999",
        "spot_trading_volume_requirement": 0,
        "futures_trading_volume_requirement": 0,
        "total_asset_requirement": 0,
        "specific_asset_symbol": null,
        "specific_asset_amount": 0,
        "spot_maker_fee_rate": 0.001,
        "spot_taker_fee_rate": 0.001,
        "futures_maker_fee_rate": 0.0002,
        "futures_taker_fee_rate": 0.0004,
        "daily_withdrawal_limit": 10000,
        "vip_gift": null,
        "vip_privileges": ["基础交易"],
        "description": "普通用户等级",
        "sort": 0
      }
    ],
    "current_page": 1,
    "per_page": 10,
    "total": 5
  }
}
```

## 2. 用户 VIP 等级维护服务使用示例

### 2.1 单个用户 VIP 等级维护

```php
use App\Http\Api\Service\User\UserVipLevelMaintenanceService;
use Hyperf\Di\Annotation\Inject;

class ExampleService
{
    #[Inject]
    private UserVipLevelMaintenanceService $vipLevelMaintenanceService;

    public function updateUserVipLevel(int $userId)
    {
        // 用户统计数据
        $userStats = [
            'spot_trading_volume' => 100000.00,      // 现货交易量
            'futures_trading_volume' => 50000.00,    // 合约交易量
            'total_asset' => 10000.00,               // 总资产
            'specific_asset_amount' => 1000.00,      // 特定币种资产（如BTC）
        ];

        try {
            $result = $this->vipLevelMaintenanceService->maintainUserVipLevel($userId, $userStats);

            if ($result['upgraded']) {
                // 用户等级升级了
                echo "用户 {$userId} 从 {$result['from_vip_level_name']} 升级到 {$result['to_vip_level_name']}";

                // 可以在这里触发升级事件，发送通知等
                // Event::dispatch(new UserVipLevelUpgraded($userId, $result));
            } else {
                // 等级未变化，只是更新了统计数据
                echo "用户 {$userId} VIP等级统计数据已更新";
            }

            return $result;
        } catch (\Exception $e) {
            echo "维护用户VIP等级失败: " . $e->getMessage();
            return false;
        }
    }
}
```

### 2.2 批量维护用户 VIP 等级

```php
public function batchUpdateUserVipLevels(array $userIds)
{
    $userStatsArray = [];

    // 构建用户统计数据数组
    foreach ($userIds as $userId) {
        // 这里应该从实际的交易数据、资产数据中计算
        $userStatsArray[$userId] = [
            'spot_trading_volume' => $this->calculateSpotTradingVolume($userId),
            'futures_trading_volume' => $this->calculateFuturesTradingVolume($userId),
            'total_asset' => $this->calculateTotalAsset($userId),
            'specific_asset_amount' => $this->calculateSpecificAssetAmount($userId),
        ];
    }

    $result = $this->vipLevelMaintenanceService->batchMaintainUserVipLevel($userStatsArray);

    echo "批量维护完成: 总数 {$result['total_count']}, 成功 {$result['success_count']}, 失败 {$result['fail_count']}";

    return $result;
}
```

### 2.3 获取用户 VIP 等级信息

```php
public function getUserVipInfo(int $userId)
{
    $vipInfo = $this->vipLevelMaintenanceService->getUserVipLevelInfo($userId);

    if (!$vipInfo) {
        echo "用户 {$userId} 还没有VIP等级";
        return null;
    }

    echo "用户 {$userId} 当前VIP等级: {$vipInfo['vip_level_name']}";
    echo "现货手续费率: 挂单 {$vipInfo['vip_level_info']['spot_maker_fee_rate']}, 吃单 {$vipInfo['vip_level_info']['spot_taker_fee_rate']}";

    return $vipInfo;
}
```

### 2.4 标记用户已领取 VIP 礼包

```php
public function markUserVipGiftReceived(int $userId)
{
    try {
        $result = $this->vipLevelMaintenanceService->markVipGiftReceived($userId);

        if ($result) {
            echo "用户 {$userId} VIP礼包领取标记成功";
        }

        return $result;
    } catch (\Exception $e) {
        echo "标记VIP礼包领取失败: " . $e->getMessage();
        return false;
    }
}
```

## 3. 定时任务示例

### 3.1 每日 VIP 等级维护任务

```php
use Hyperf\Crontab\Annotation\Crontab;

#[Crontab(rule: "0 2 * * *", name: "daily_vip_level_maintenance")]
class DailyVipLevelMaintenanceTask
{
    #[Inject]
    private UserVipLevelMaintenanceService $vipLevelMaintenanceService;

    public function execute()
    {
        // 获取需要维护VIP等级的用户列表
        $users = User::query()
            ->where('status', Status::NORMAL)
            ->pluck('id')
            ->toArray();

        $userStatsArray = [];

        foreach ($users as $userId) {
            // 计算用户过去30天的统计数据
            $userStatsArray[$userId] = [
                'spot_trading_volume' => $this->calculateLast30DaysSpotVolume($userId),
                'futures_trading_volume' => $this->calculateLast30DaysFuturesVolume($userId),
                'total_asset' => $this->calculateCurrentTotalAsset($userId),
                'specific_asset_amount' => $this->calculateCurrentBtcAmount($userId),
            ];
        }

        // 批量维护
        $result = $this->vipLevelMaintenanceService->batchMaintainUserVipLevel($userStatsArray);

        // 记录日志
        Log::info('每日VIP等级维护完成', $result);
    }
}
```

## 4. 事件监听示例

### 4.1 交易完成后触发 VIP 等级检查

```php
use App\Event\TradeCompletedEvent;
use Hyperf\Event\Annotation\Listener;
use Hyperf\Event\Contract\ListenerInterface;

#[Listener]
class TradeCompletedListener implements ListenerInterface
{
    #[Inject]
    private UserVipLevelMaintenanceService $vipLevelMaintenanceService;

    public function listen(): array
    {
        return [TradeCompletedEvent::class];
    }

    public function process(object $event)
    {
        if ($event instanceof TradeCompletedEvent) {
            $userId = $event->getUserId();

            // 重新计算用户统计数据
            $userStats = [
                'spot_trading_volume' => $this->calculateUserSpotVolume($userId),
                'futures_trading_volume' => $this->calculateUserFuturesVolume($userId),
                'total_asset' => $this->calculateUserTotalAsset($userId),
                'specific_asset_amount' => $this->calculateUserSpecificAsset($userId),
            ];

            // 维护用户VIP等级
            try {
                $result = $this->vipLevelMaintenanceService->maintainUserVipLevel($userId, $userStats);

                if ($result['upgraded']) {
                    // 发送升级通知
                    // NotificationService::sendVipUpgradeNotification($userId, $result);
                }
            } catch (\Exception $e) {
                Log::error('交易完成后维护VIP等级失败', [
                    'user_id' => $userId,
                    'error' => $e->getMessage()
                ]);
            }
        }
    }
}
```

## 5. 注意事项

1. **性能考虑**: 批量维护时建议分批处理，避免一次处理过多用户
2. **事务安全**: 所有 VIP 等级变更都在事务中进行，确保数据一致性
3. **日志记录**: 重要操作都有详细的日志记录，便于问题排查
4. **业务规则**: 当前实现不支持自动降级，只支持升级和统计数据更新
5. **缓存策略**: 可以考虑对 VIP 等级信息进行缓存，提高查询性能
6. **通知机制**: VIP 等级升级后可以触发相应的通知事件
