# CPX_EXCHANGE 编码风格与规范文档

## 1. 文件头部规范

### 1.1 PHP 文件声明

```php
<?php

declare(strict_types=1);
/**
 * 策略平台API
 * {模块描述}
 */
```

### 1.2 文件注释格式

```php
/**
 * {类名}
 * Author:{作者名}
 * Contact:tg:@chenmaq
 * Version:1.0
 * Date:{日期}
 * Website:{网站}
 */
```

## 2. 命名规范

### 2.1 类命名

- **模型类**: 使用单数名词，首字母大写，如 `User`、`Agent`、`AgentClient`
- **服务类**: 以 `Service` 结尾，如 `UserService`、`AgentService`
- **控制器类**: 以 `Controller` 结尾，如 `UserController`
- **请求类**: 以 `Request` 结尾，如 `RegisterByEmailRequest`
- **枚举类**: 使用描述性名词，如 `Status`、`RegisterType`

### 2.2 方法命名

- 使用驼峰命名法
- 动词开头，描述性强
- 常用前缀：`get`、`set`、`create`、`update`、`delete`、`find`、`register`、`login`

```php
public function registerByEmail(array $data): array
public function getUserInfo(): array
public function setUserCache(User $user): void
```

### 2.3 变量命名

- 使用驼峰命名法
- 描述性强，避免缩写
- 布尔变量使用 `is`、`has`、`can` 前缀

```php
$inviteCode = 'Y' . strtoupper(substr(md5(uniqid() . microtime(true)), 0, 6));
$isManagerProcess = true;
$hasPermission = false;
```

### 2.4 常量命名

- 使用全大写字母，下划线分隔
- 字段常量使用 `FIELD_` 前缀

```php
public const FIELD_ID = 'id';
public const FIELD_USER_ID = 'user_id';
public const FIELD_PARENT_AGENT_ID = 'parent_agent_id';
```

## 3. 目录结构规范

### 3.1 模块目录结构

```
app/
├── Http/
│   ├── Api/
│   │   ├── Controller/
│   │   ├── Service/
│   │   ├── Request/
│   │   ├── Resource/
│   │   └── Middleware/
│   ├── Admin/
│   └── Common/
├── Model/
│   ├── User/
│   │   └── Enums/          # 枚举类放在各模块模型目录下的Enums中
│   ├── Agent/
│   │   └── Enums/
│   └── {Module}/
│       └── Enums/
└── Service/
```

### 3.2 文件命名规范

- 控制器: `{Entity}Controller.php`
- 服务类: `{Entity}Service.php`
- 模型: `{Entity}.php`
- 请求验证: `{Action}Request.php`
- 枚举: `{Property}.php`

## 4. 模型规范

### 4.1 模型基本结构

```php
<?php

declare(strict_types=1);
/**
 * 策略平台API
 * {实体}模型
 */

namespace App\Model\{Module};

use App\QueryBuilder\Model;
use App\Model\{Module}\Enums\Status;
use Carbon\Carbon;

/**
 * @property int $id 主键ID
 * @property string $name 名称
 * @property Status $status 状态
 * @property Carbon|null $created_at 创建时间
 * @property Carbon|null $updated_at 更新时间
 */
final class {Entity} extends Model
{
    // 字段常量定义
    public const FIELD_ID = 'id';
    public const FIELD_NAME = 'name';
    public const FIELD_STATUS = 'status';

    // 表名
    protected ?string $table = '{table_name}';

    // 可批量赋值字段
    protected array $fillable = [
        'id', // 主键ID
        'name', // 名称
        'status', // 状态
        'created_at', // 创建时间
        'updated_at', // 更新时间
    ];

    // 隐藏字段
    protected array $hidden = [
        'password',
        'deleted_at'
    ];

    // 类型转换
    protected array $casts = [
        'id' => 'integer', // 主键ID
        'status' => Status::class, // 状态枚举
        'created_at' => 'datetime', // 创建时间
        'updated_at' => 'datetime', // 更新时间
    ];
}
```

### 4.2 字段注释规范

- 每个字段必须有详细的中文注释
- `fillable` 和 `casts` 数组中每个字段都要有行内注释
- 使用 `@property` 注解声明所有字段

### 4.3 关联关系定义

```php
/**
 * 获取关联的用户模型
 */
public function user()
{
    return $this->belongsTo(User::class, self::FIELD_USER_ID, User::FIELD_ID);
}
```

## 5. 枚举类规范

### 5.1 枚举基本结构

```php
<?php

declare(strict_types=1);
/**
 * 策略平台API
 * {描述}枚举
 */

namespace App\Model\{Module}\Enums;

use Hyperf\Constants\Annotation\Constants;
use Hyperf\Constants\Annotation\Message;
use Hyperf\Constants\EnumConstantsTrait;

#[Constants]
enum Status: int
{
    use EnumConstantsTrait;

    /**
     * 正常
     */
    #[Message('user.enums.status.1')]
    case NORMAL = 1;

    /**
     * 禁用
     */
    #[Message('user.enums.status.2')]
    case DISABLED = 2;
}
```

### 5.2 枚举命名规范

- 枚举值使用全大写字母，下划线分隔
- 每个枚举值必须有中文注释
- 使用 `#[Message]` 注解定义国际化消息
- **重要**: 枚举类必须放在各模块模型目录下的 `Enums` 中，如 `app/Model/User/Enums/Status.php`

## 6. 服务类规范

### 6.1 服务类目录结构

- **API 服务类**: 放在 `app/Http/Api/Service/{Module}/` 目录下
- **通用服务类**: 放在 `app/Service/` 目录下
- **所有服务类**: 必须继承 `BaseService`

### 6.2 服务类基本结构

```php
<?php

declare(strict_types=1);
/**
 * {服务描述}
 * Author:{作者}
 * Contact:tg:@chenmaq
 * Version:1.0
 * Date:{日期}
 * Website:{网站}
 */

namespace App\Http\Api\Service\{Module};

use App\Http\Api\Service\BaseService;
use App\Exception\BusinessException;
use App\Http\Common\ResultCode;

class {Entity}Service extends BaseService
{
    // 服务实现
}
```

### 6.3 数据返回规范

- **避免多余的数据组装**: 服务层应直接返回模型对象或集合，避免手动组装数组
- **正确示例**: `return User::query()->find($id);`
- **错误示例**: 手动组装用户数据数组

```php
// ❌ 错误：多余的数据组装
public function getUserInfo(int $userId): array
{
    $user = User::query()->find($userId);
    return [
        'id' => $user->id,
        'name' => $user->name,
        'email' => $user->email,
        // ... 其他字段
    ];
}

// ✅ 正确：直接返回模型
public function getUserInfo(int $userId): ?User
{
    return User::query()->find($userId);
}
```

### 6.4 事务处理规范

```php
// 事务
Db::beginTransaction();
try {
    // 业务逻辑
    $user = new User();
    $user->save();

    Db::commit();
} catch (\Throwable $th) {
    Db::rollBack();
    throw new BusinessException(ResultCode::FAIL, '操作失败，' . $th->getMessage());
}
```

## 7. 控制器规范

### 7.1 控制器基本结构

```php
<?php

declare(strict_types=1);

namespace App\Http\Api\Controller\{Module};

use App\Http\Api\Middleware\TokenMiddleware;
use App\Http\Common\Controller\AbstractController;
use App\Http\Common\Result;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\Middleware;
use Hyperf\HttpServer\Annotation\PostMapping;

#[Controller(prefix: 'api/{module}')]
#[Middleware(middleware: TokenMiddleware::class, priority: 100)]
final class {Entity}Controller extends AbstractController
{
    #[Inject]
    private readonly {Entity}Service ${entity}Service;

    /**
     * {方法描述}
     */
    #[PostMapping('{action}')]
    public function {action}({Request} $request): Result
    {
        return $this->success(
            $this->{entity}Service->{action}($request->all())
        );
    }
}
```

### 7.2 中间件使用规范

- **登录验证**: 所有需要登录的接口必须添加 `TokenMiddleware`
- **类级别中间件**: 如果整个控制器都需要登录，在类上添加中间件
- **方法级别中间件**: 如果只有部分方法需要特殊中间件，在方法上添加

```php
// 类级别中间件（推荐）
#[Controller(prefix: 'api/user')]
#[Middleware(middleware: TokenMiddleware::class, priority: 100)]
final class UserController extends AbstractController

// 方法级别中间件
#[PostMapping('action')]
#[Middleware(middleware: PermissionMiddleware::class, priority: 99)]
public function action(): Result

// 中间件优先级
// 验证登录: priority: 100
// 验证权限: priority: 99
// 记录操作日志: priority: 98
```

## 8. 请求验证规范

### 8.1 请求类基本结构

当验证器继承 `\App\Http\Api\Request\BaseFormRequest` 时，支持 `{控制器方法名}Rules` 方式定义各控制器方法的验证器：

```php
<?php

declare(strict_types=1);

namespace App\Http\Api\Request\{Module};

use App\Http\Api\Request\BaseFormRequest;

class {Entity}Request extends BaseFormRequest
{
    /**
     * 注册验证规则 - 对应控制器的 register 方法
     */
    public function registerRules(): array
    {
        return [
            'email' => ['required', 'email', 'unique:cpx_user,email'],
            'password' => ['required', 'string', 'min:6'],
            'code' => ['required', 'string'],
        ];
    }

    /**
     * 登录验证规则 - 对应控制器的 login 方法
     */
    public function loginRules(): array
    {
        return [
            'username' => ['required', 'string'],
            'password' => ['required', 'string'],
        ];
    }

    /**
     * 列表验证规则 - 对应控制器的 list 方法
     */
    public function listRules(): array
    {
        return [
            'page' => ['integer', 'min:1'],
            'page_size' => ['integer', 'min:1', 'max:100'],
        ];
    }

    /**
     * 字段映射名称
     */
    public function attributes(): array
    {
        return [
            'email' => '邮箱',
            'password' => '密码',
            'code' => '验证码',
        ];
    }
}
```

### 8.2 验证规则命名规范

- 方法名格式：`{控制器方法名}Rules()`
- 如控制器方法为 `registerByEmail`，则验证方法为 `registerByEmailRules()`
- 如控制器方法为 `bindPhone`，则验证方法为 `bindPhoneRules()`

## 9. 分页列表接口规范

### 9.1 使用 QueryBuilder 构建查询

**重要**: 分页列表接口必须使用 `App\QueryBuilder\QueryBuilder` 构建查询：

```php
use App\QueryBuilder\QueryBuilder;

/**
 * 获取用户列表
 */
public function list(RequestInterface $request): mixed
{
    // 构建固定查询条件
    $query = User::query()->where('status', Status::NORMAL);

    return QueryBuilder::for($query, $request)
        ->filters(['username', 'email', 'phone']) // 自动包含时间和ID过滤器
        ->allowedSorts(['id', 'username', 'created_at'])
        ->defaultSort('-created_at')
        ->pagex(); // 支持 page_size=-1 获取所有记录
}
```

### 9.2 QueryBuilder 固定条件规范

- **固定条件先构建**: QueryBuilder 的固定查询条件应该先构建好，再传入 `QueryBuilder::for()`
- **正确示例**: `QueryBuilder::for($query, $request)` 其中 `$query` 是预先构建的查询
- **错误示例**: 在 `QueryBuilder::for()` 后使用 `where()` 方法

### 9.3 QueryBuilder 核心功能

#### 9.3.1 filters() 方法

- 自动包含 ID 和时间范围过滤器
- 支持的时间过滤器：
  - `CreatedAtOfDay` - 指定日期当天
  - `CreatedAtBefore` - 创建时间之前
  - `CreatedAtAfter` - 创建时间之后
  - `CreatedAtBetween` - 创建时间区间
  - `UpdatedAtBefore` - 更新时间之前
  - `UpdatedAtAfter` - 更新时间之后
  - `UpdatedAtBetween` - 更新时间区间

#### 9.3.2 pagex() 方法

- 支持通过 `page_size=-1` 获取所有记录
- 当 `page_size <= 0` 时，`page()` 方法默认每页 15 条
- **避免重复接口**: 不要单独创建不分页接口，统一使用 `pagex()` 方法

#### 9.3.3 查询参数示例

```
?filter[username]=admin&filter[CreatedAtBetween]=2024-01-01,2024-12-31&sort=-created_at&page=1&page_size=15
```

### 9.4 控制器中的使用

```php
/**
 * 用户列表
 */
#[GetMapping('list')]
public function list(UserRequest $request): Result
{
    $result = QueryBuilder::for(User::class, $request)
        ->filters(['username', 'email', 'status'])
        ->allowedSorts(['id', 'username', 'created_at'])
        ->defaultSort('-created_at')
        ->pagex();

    return $this->success($result);
}
```

## 10. 数据库迁移规范

### 10.1 迁移文件结构

```php
<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cpx_user', function (Blueprint $table) {
            $table->bigIncrements('id')->comment('用户ID，主键');
            $table->string('username', 50)->unique()->comment('用户名');
            $table->string('email', 100)->nullable()->unique()->comment('邮箱');
            $table->timestamps();

            // 索引
            $table->index(['username']);
            $table->index(['email']);

            $table->comment('交易所用户表');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cpx_user');
    }
};
```

### 10.2 字段定义规范

- 每个字段必须有 `comment()` 注释
- 字符串字段必须指定长度
- 外键字段使用 `bigInteger()` 类型
- 必要时添加索引

## 11. 注释规范

### 11.1 类注释

```php
/**
 * @property int $id 用户ID，主键
 * @property string $username 用户名
 * @property Status $status 状态
 * @property Carbon|null $created_at 创建时间
 */
```

### 11.2 方法注释

```php
/**
 * 用户邮箱注册
 *
 * @param array $data 注册数据
 * @return array 注册结果
 * @throws BusinessException 业务异常
 */
public function registerByEmail(array $data): array
```

### 11.3 行内注释

```php
// 事务
Db::beginTransaction();

// 创建用户
$username = 'CPX-' . strtoupper(substr(md5(uniqid() . microtime(true)), 0, 7));

// 如果父级是代理商，则当前用户为代理商直客
$this->agentClientCreate($user, $parent->agent_id, $inviterInfo['agentInviteCode']);
```

## 12. 异常处理规范

### 12.1 业务异常

```php
throw new BusinessException(ResultCode::FAIL, '注册失败，' . $th->getMessage());
```

### 12.2 异常捕获

```php
try {
    // 业务逻辑
} catch (\Throwable $th) {
    // 异常处理
    throw new BusinessException(ResultCode::FAIL, $th->getMessage());
}
```

## 13. 代码格式规范

### 13.1 缩进和空格

- 使用 4 个空格缩进
- 方法之间空一行
- 逻辑块之间空一行

### 13.2 数组格式

```php
protected array $fillable = [
    'id', // 主键ID
    'username', // 用户名
    'email', // 邮箱
];
```

### 13.3 方法链调用

```php
$user = User::query()
    ->where('status', Status::NORMAL)
    ->where('email', $email)
    ->first();
```

## 14. 安全规范

### 14.1 数据验证

- 所有用户输入必须验证
- 使用 FormRequest 进行参数验证
- 敏感操作需要额外验证

### 14.2 数据库操作

- 使用 Eloquent ORM，避免原生 SQL
- 重要操作使用事务
- 敏感字段需要加密存储

### 14.3 权限控制

- 使用中间件进行权限验证
- 敏感操作需要权限检查
- 记录重要操作日志

---

若无特别说明不要在代码中记录日志（$this->logger）

**注意**: 这份编码规范文档基于项目中用户系统和代理系统的实际代码风格总结而成，后续开发请严格遵循这些规范，确保代码的一致性和可维护性。特别注意枚举类的目录结构、请求验证的方法命名规范以及分页列表接口必须使用 QueryBuilder 的要求。
