# 跟单尊享模式需求文档

## 1. 功能概述

跟单尊享模式是一种高级跟单交易模式，允许交易专家设置白名单邀请、未结仓位保护、分润比例设置和跟单者个性化管理。该模式旨在为交易专家提供一个隐私的带单环境，同时为跟单者提供全球顶级交易专家的资源支持与服务。

## 2. 核心功能

### 2.1 白名单邀请功能

#### 2.1.1 邀请链接管理

- 创建白名单邀请链接
  - 设置邀请标题（选填）
  - 设置链接有效期
  - 设置最大邀请人数
  - 生成邀请链接或分享链接
- 管理现有邀请链接
  - 查看链接状态
  - 失效/删除链接
  - 查看链接使用情况

#### 2.1.2 跟单人数限制

- 基于交易专家等级的跟单人数上限
- 支持 BGB 拓展满员 10%的额外名额
- 多个邀请链接的人数统一管理

### 2.2 未结仓位保护

#### 2.2.1 仓位可见性控制

- 仅对尊享模式跟单者展示未结仓位
- 对普通用户隐藏仓位信息
- 保护交易策略隐私

#### 2.2.2 仓位数据管理

- 实时更新仓位状态
- 分组展示（尊享/普通）
- 历史仓位记录

### 2.3 分润比例设置

#### 2.3.1 分润比例管理

- 支持 0%~99%范围内自由设置
- 每日最多修改 3 次
- 无需锁仓 BGB
- 新旧订单分润比例独立计算

#### 2.3.2 分润通知

- 分润比例变更通知
- 分润收益统计
- 历史分润记录

### 2.4 跟单者管理

#### 2.4.1 跟单者信息查看

- 用户头像/昵称
- 跟单时间
- 当前状态（待跟单/跟单中/已结束）
- 贡献分润统计
- 交易数据分析

#### 2.4.2 跟单者权限控制

- 添加/移除跟单者
- 跟单者黑名单管理
- 跟单者分组管理

## 3. 数据库设计

### 3.1 交易专家尊享配置表（trader_exclusive_config）

```sql
CREATE TABLE `trader_exclusive_config` (
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `trader_id` bigint(20) unsigned NOT NULL COMMENT '交易专家ID',
    `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '尊享模式状态：0-关闭，1-开启',
    `position_protection` tinyint(1) NOT NULL DEFAULT '0' COMMENT '未结仓位保护：0-关闭，1-开启',
    `profit_ratio` decimal(5,2) NOT NULL DEFAULT '0.00' COMMENT '分润比例',
    `max_followers` int(11) NOT NULL COMMENT '最大跟随人数',
    `daily_ratio_changes` int(11) NOT NULL DEFAULT '0' COMMENT '今日分润比例修改次数',
    `ratio_change_date` date DEFAULT NULL COMMENT '分润比例修改日期',
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_trader_id` (`trader_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='交易专家尊享配置表';
```

### 3.2 白名单邀请链接表（whitelist_invitation）

```sql
CREATE TABLE `whitelist_invitation` (
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `trader_id` bigint(20) unsigned NOT NULL COMMENT '交易专家ID',
    `title` varchar(100) DEFAULT NULL COMMENT '邀请标题',
    `invitation_code` varchar(32) NOT NULL COMMENT '邀请码',
    `max_users` int(11) NOT NULL COMMENT '最大邀请人数',
    `current_users` int(11) NOT NULL DEFAULT '0' COMMENT '当前邀请人数',
    `expired_at` timestamp NOT NULL COMMENT '过期时间',
    `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1-有效，2-已失效',
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_invitation_code` (`invitation_code`),
    KEY `idx_trader_id` (`trader_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='白名单邀请链接表';
```

### 3.3 跟单者关系表（follower_relationship）

```sql
CREATE TABLE `follower_relationship` (
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `trader_id` bigint(20) unsigned NOT NULL COMMENT '交易专家ID',
    `follower_id` bigint(20) unsigned NOT NULL COMMENT '跟单者ID',
    `invitation_id` bigint(20) unsigned NOT NULL COMMENT '邀请链接ID',
    `profit_ratio` decimal(5,2) NOT NULL COMMENT '分润比例',
    `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1-待跟单，2-跟单中，3-已结束',
    `start_time` timestamp NULL DEFAULT NULL COMMENT '开始跟单时间',
    `end_time` timestamp NULL DEFAULT NULL COMMENT '结束跟单时间',
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_trader_follower` (`trader_id`,`follower_id`),
    KEY `idx_invitation_id` (`invitation_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='跟单者关系表';
```

## 4. 接口设计

### 4.1 交易专家接口

#### 4.1.1 尊享模式配置

```php
// 开启/关闭尊享模式
POST /api/v1/trader/exclusive/toggle
// 设置未结仓位保护
POST /api/v1/trader/exclusive/position-protection
// 设置分润比例
POST /api/v1/trader/exclusive/profit-ratio
```

#### 4.1.2 白名单邀请管理

```php
// 创建邀请链接
POST /api/v1/trader/invitation/create
// 获取邀请链接列表
GET /api/v1/trader/invitation/list
// 失效邀请链接
POST /api/v1/trader/invitation/invalidate
```

#### 4.1.3 跟单者管理

```php
// 获取跟单者列表
GET /api/v1/trader/followers
// 移除跟单者
POST /api/v1/trader/follower/remove
// 查看跟单者详情
GET /api/v1/trader/follower/detail
```

### 4.2 跟单者接口

#### 4.2.1 跟单操作

```php
// 通过邀请链接加入
POST /api/v1/follower/join
// 查看跟单状态
GET /api/v1/follower/status
// 取消跟单
POST /api/v1/follower/cancel
```

## 5. 注意事项

### 5.1 尊享模式开启影响

- 不在首页顶级交易专家、全部交易专家、排行榜展示排名
- 原有已跟单用户不受影响
- 仅可通过白名单链接进行跟单
- 所有分享链接仅白名单用户可用

### 5.2 尊享模式关闭影响

- 原有已跟单用户不受影响
- 所有白名单链接失效
- 分润比例恢复至常规模式

### 5.3 安全考虑

- 交易策略保护
- 用户数据隐私
- 资金安全保障
- 异常行为监控

## 6. 实现计划

### 6.1 第一阶段（基础功能）

- 尊享模式开关
- 白名单邀请系统
- 基础跟单功能

### 6.2 第二阶段（核心功能）

- 未结仓位保护
- 分润比例管理
- 跟单者管理系统

### 6.3 第三阶段（优化完善）

- 数据统计分析
- 性能优化
- 用户体验改进
