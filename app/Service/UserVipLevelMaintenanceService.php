<?php

declare(strict_types=1);
/**
 * 用户VIP等级维护服务
 * Author:chenmaq
 * Contact:tg:@chenmaq
 * Version:1.0
 * Date:2025-01-15
 * Website:bbbtrade.net
 */

namespace App\Service;

use App\Exception\BusinessException;
use App\Http\Common\ResultCode;
use App\Model\User\User;
use App\Model\User\UserVipLevel;
use App\Model\User\VipLevel;
use App\Model\User\Enums\VipLevelStatus;
use Carbon\Carbon;
use Hyperf\DbConnection\Db;
use Hyperf\Logger\LoggerFactory;
use Psr\Log\LoggerInterface;

class UserVipLevelMaintenanceService
{
    private LoggerInterface $logger;

    public function __construct(LoggerFactory $loggerFactory)
    {
        $this->logger = $loggerFactory->get('user_vip_level');
    }

    /**
     * 根据用户数据维护用户VIP等级
     *
     * @param int $userId 用户ID
     * @param array $userStats 用户统计数据
     * @return array 维护结果
     * @throws BusinessException
     */
    public function maintainUserVipLevel(int $userId, array $userStats): array
    {
        // 验证用户是否存在
        $user = User::query()->find($userId);
        if (!$user) {
            throw new BusinessException(ResultCode::FAIL, '用户不存在');
        }

        // 获取用户当前VIP等级
        $currentUserVipLevel = UserVipLevel::getUserActiveVipLevel($userId);

        // 计算用户应该达到的VIP等级
        $targetVipLevel = $this->calculateTargetVipLevel($userStats);

        if (!$targetVipLevel) {
            $this->logger->warning('未找到匹配的VIP等级', [
                'user_id' => $userId,
                'user_stats' => $userStats
            ]);
            return [
                'upgraded' => false,
                'message' => '未找到匹配的VIP等级'
            ];
        }

        // 如果用户当前没有VIP等级，直接设置为目标等级
        if (!$currentUserVipLevel) {
            return $this->setUserVipLevel($userId, $targetVipLevel, $userStats);
        }

        // 如果目标等级高于当前等级，进行升级
        if ($targetVipLevel->level > $currentUserVipLevel->vipLevel->level) {
            return $this->upgradeUserVipLevel($userId, $currentUserVipLevel, $targetVipLevel, $userStats);
        }

        // 如果目标等级等于当前等级，更新统计数据
        if ($targetVipLevel->level === $currentUserVipLevel->vipLevel->level) {
            return $this->updateUserVipLevelStats($currentUserVipLevel, $userStats);
        }

        // 如果目标等级低于当前等级，检查是否需要降级（根据业务规则决定）
        return $this->handleVipLevelDowngrade($userId, $currentUserVipLevel, $targetVipLevel, $userStats);
    }

    /**
     * 计算用户应该达到的VIP等级
     */
    private function calculateTargetVipLevel(array $userStats): ?VipLevel
    {
        $spotTradingVolume = $userStats['spot_trading_volume'] ?? 0;
        $futuresTradingVolume = $userStats['futures_trading_volume'] ?? 0;
        $totalAsset = $userStats['total_asset'] ?? 0;
        $specificAssetAmount = $userStats['specific_asset_amount'] ?? 0;

        // 获取所有启用的VIP等级，按等级从高到低排序
        $vipLevels = VipLevel::query()
            ->where(VipLevel::FIELD_STATUS, VipLevelStatus::ENABLED)
            ->orderBy(VipLevel::FIELD_LEVEL, 'desc')
            ->get();

        // 从最高等级开始检查，找到用户满足条件的最高等级
        foreach ($vipLevels as $vipLevel) {
            if ($this->checkVipLevelRequirements($vipLevel, $spotTradingVolume, $futuresTradingVolume, $totalAsset, $specificAssetAmount)) {
                return $vipLevel;
            }
        }

        // 如果都不满足，返回最低等级（VIP0）
        return VipLevel::query()
            ->where(VipLevel::FIELD_STATUS, VipLevelStatus::ENABLED)
            ->orderBy(VipLevel::FIELD_LEVEL, 'asc')
            ->first();
    }

    /**
     * 检查是否满足VIP等级要求
     */
    private function checkVipLevelRequirements(
        VipLevel $vipLevel,
        float $spotTradingVolume,
        float $futuresTradingVolume,
        float $totalAsset,
        float $specificAssetAmount
    ): bool {
        // 检查现货交易量要求
        if (
            $vipLevel->spot_trading_volume_requirement > 0 &&
            $spotTradingVolume < $vipLevel->spot_trading_volume_requirement
        ) {
            return false;
        }

        // 检查合约交易量要求
        if (
            $vipLevel->futures_trading_volume_requirement > 0 &&
            $futuresTradingVolume < $vipLevel->futures_trading_volume_requirement
        ) {
            return false;
        }

        // 检查总资产要求
        if (
            $vipLevel->total_asset_requirement > 0 &&
            $totalAsset < $vipLevel->total_asset_requirement
        ) {
            return false;
        }

        // 检查特定币种资产要求
        if (
            $vipLevel->specific_asset_amount > 0 &&
            $specificAssetAmount < $vipLevel->specific_asset_amount
        ) {
            return false;
        }

        return true;
    }

    /**
     * 设置用户VIP等级（首次设置）
     */
    private function setUserVipLevel(int $userId, VipLevel $vipLevel, array $userStats): array
    {
        // 事务
        Db::beginTransaction();
        try {
            $userVipLevel = new UserVipLevel();
            $userVipLevel->user_id = $userId;
            $userVipLevel->vip_level_id = $vipLevel->id;
            $userVipLevel->current_spot_trading_volume = $userStats['spot_trading_volume'] ?? 0;
            $userVipLevel->current_futures_trading_volume = $userStats['futures_trading_volume'] ?? 0;
            $userVipLevel->current_total_asset = $userStats['total_asset'] ?? 0;
            $userVipLevel->current_specific_asset_amount = $userStats['specific_asset_amount'] ?? 0;
            $userVipLevel->level_achieved_at = Carbon::now();
            $userVipLevel->is_active = UserVipLevel::IS_ACTIVE_YES;
            $userVipLevel->gift_received = UserVipLevel::GIFT_RECEIVED_NO;
            $userVipLevel->save();

            Db::commit();

            $this->logger->info('用户VIP等级设置成功', [
                'user_id' => $userId,
                'vip_level_id' => $vipLevel->id,
                'vip_level_name' => $vipLevel->name
            ]);

            return [
                'upgraded' => true,
                'message' => 'VIP等级设置成功',
                'from_level' => null,
                'to_level' => $vipLevel->level,
                'vip_level_name' => $vipLevel->name,
                'user_vip_level_id' => $userVipLevel->id
            ];
        } catch (\Throwable $th) {
            Db::rollBack();
            $this->logger->error('设置用户VIP等级失败', [
                'user_id' => $userId,
                'error' => $th->getMessage()
            ]);
            throw new BusinessException(ResultCode::FAIL, '设置VIP等级失败，' . $th->getMessage());
        }
    }

    /**
     * 升级用户VIP等级
     */
    private function upgradeUserVipLevel(
        int $userId,
        UserVipLevel $currentUserVipLevel,
        VipLevel $targetVipLevel,
        array $userStats
    ): array {
        // 事务
        Db::beginTransaction();
        try {
            // 将当前等级设为非激活状态
            $currentUserVipLevel->is_active = UserVipLevel::IS_ACTIVE_NO;
            $currentUserVipLevel->save();

            // 创建新的VIP等级记录
            $newUserVipLevel = new UserVipLevel();
            $newUserVipLevel->user_id = $userId;
            $newUserVipLevel->vip_level_id = $targetVipLevel->id;
            $newUserVipLevel->current_spot_trading_volume = $userStats['spot_trading_volume'] ?? 0;
            $newUserVipLevel->current_futures_trading_volume = $userStats['futures_trading_volume'] ?? 0;
            $newUserVipLevel->current_total_asset = $userStats['total_asset'] ?? 0;
            $newUserVipLevel->current_specific_asset_amount = $userStats['specific_asset_amount'] ?? 0;
            $newUserVipLevel->level_achieved_at = Carbon::now();
            $newUserVipLevel->is_active = UserVipLevel::IS_ACTIVE_YES;
            $newUserVipLevel->gift_received = UserVipLevel::GIFT_RECEIVED_NO;
            $newUserVipLevel->save();

            Db::commit();

            $this->logger->info('用户VIP等级升级成功', [
                'user_id' => $userId,
                'from_level' => $currentUserVipLevel->vipLevel->level,
                'to_level' => $targetVipLevel->level,
                'from_vip_level_name' => $currentUserVipLevel->vipLevel->name,
                'to_vip_level_name' => $targetVipLevel->name
            ]);

            return [
                'upgraded' => true,
                'message' => 'VIP等级升级成功',
                'from_level' => $currentUserVipLevel->vipLevel->level,
                'to_level' => $targetVipLevel->level,
                'from_vip_level_name' => $currentUserVipLevel->vipLevel->name,
                'to_vip_level_name' => $targetVipLevel->name,
                'user_vip_level_id' => $newUserVipLevel->id
            ];
        } catch (\Throwable $th) {
            Db::rollBack();
            $this->logger->error('升级用户VIP等级失败', [
                'user_id' => $userId,
                'error' => $th->getMessage()
            ]);
            throw new BusinessException(ResultCode::FAIL, '升级VIP等级失败，' . $th->getMessage());
        }
    }

    /**
     * 更新用户VIP等级统计数据
     */
    private function updateUserVipLevelStats(UserVipLevel $userVipLevel, array $userStats): array
    {
        // 事务
        Db::beginTransaction();
        try {
            $userVipLevel->current_spot_trading_volume = $userStats['spot_trading_volume'] ?? 0;
            $userVipLevel->current_futures_trading_volume = $userStats['futures_trading_volume'] ?? 0;
            $userVipLevel->current_total_asset = $userStats['total_asset'] ?? 0;
            $userVipLevel->current_specific_asset_amount = $userStats['specific_asset_amount'] ?? 0;
            $userVipLevel->save();

            Db::commit();

            $this->logger->info('用户VIP等级统计数据更新成功', [
                'user_id' => $userVipLevel->user_id,
                'vip_level_id' => $userVipLevel->vip_level_id
            ]);

            return [
                'upgraded' => false,
                'message' => 'VIP等级统计数据更新成功',
                'current_level' => $userVipLevel->vipLevel->level,
                'vip_level_name' => $userVipLevel->vipLevel->name
            ];
        } catch (\Throwable $th) {
            Db::rollBack();
            $this->logger->error('更新用户VIP等级统计数据失败', [
                'user_id' => $userVipLevel->user_id,
                'error' => $th->getMessage()
            ]);
            throw new BusinessException(ResultCode::FAIL, '更新VIP等级统计数据失败，' . $th->getMessage());
        }
    }

    /**
     * 处理VIP等级降级（根据业务规则决定是否允许降级）
     */
    private function handleVipLevelDowngrade(
        int $userId,
        UserVipLevel $currentUserVipLevel,
        VipLevel $targetVipLevel,
        array $userStats
    ): array {
        // 根据业务规则，这里可以选择是否允许降级
        // 一般情况下，VIP等级不会自动降级，只会升级
        // 如果需要降级功能，可以在这里实现

        $this->logger->info('用户VIP等级无需降级', [
            'user_id' => $userId,
            'current_level' => $currentUserVipLevel->vipLevel->level,
            'target_level' => $targetVipLevel->level
        ]);

        // 更新统计数据但保持当前等级
        return $this->updateUserVipLevelStats($currentUserVipLevel, $userStats);
    }

    /**
     * 批量维护用户VIP等级
     *
     * @param array $userStatsArray 用户统计数据数组，格式：[user_id => user_stats]
     * @return array 批量维护结果
     */
    public function batchMaintainUserVipLevel(array $userStatsArray): array
    {
        $results = [];
        $successCount = 0;
        $failCount = 0;

        foreach ($userStatsArray as $userId => $userStats) {
            try {
                $result = $this->maintainUserVipLevel($userId, $userStats);
                $results[$userId] = $result;
                $successCount++;
            } catch (\Throwable $th) {
                $results[$userId] = [
                    'upgraded' => false,
                    'message' => '维护失败：' . $th->getMessage(),
                    'error' => true
                ];
                $failCount++;

                $this->logger->error('批量维护用户VIP等级失败', [
                    'user_id' => $userId,
                    'error' => $th->getMessage()
                ]);
            }
        }

        $this->logger->info('批量维护用户VIP等级完成', [
            'total_count' => count($userStatsArray),
            'success_count' => $successCount,
            'fail_count' => $failCount
        ]);

        return [
            'total_count' => count($userStatsArray),
            'success_count' => $successCount,
            'fail_count' => $failCount,
            'results' => $results
        ];
    }

    /**
     * 获取用户当前VIP等级信息
     */
    public function getUserVipLevelInfo(int $userId): ?array
    {
        $userVipLevel = UserVipLevel::getUserActiveVipLevel($userId);

        if (!$userVipLevel) {
            return null;
        }

        return [
            'user_id' => $userVipLevel->user_id,
            'vip_level_id' => $userVipLevel->vip_level_id,
            'vip_level_name' => $userVipLevel->vipLevel->name,
            'vip_level' => $userVipLevel->vipLevel->level,
            'current_spot_trading_volume' => $userVipLevel->current_spot_trading_volume,
            'current_futures_trading_volume' => $userVipLevel->current_futures_trading_volume,
            'current_total_asset' => $userVipLevel->current_total_asset,
            'current_specific_asset_amount' => $userVipLevel->current_specific_asset_amount,
            'level_achieved_at' => $userVipLevel->level_achieved_at->toDateTimeString(),
            'level_expires_at' => $userVipLevel->level_expires_at?->toDateTimeString(),
            'is_active' => $userVipLevel->is_active === UserVipLevel::IS_ACTIVE_YES,
            'gift_received' => $userVipLevel->gift_received === UserVipLevel::GIFT_RECEIVED_YES,
            'vip_level_info' => [
                'spot_maker_fee_rate' => $userVipLevel->vipLevel->spot_maker_fee_rate,
                'spot_taker_fee_rate' => $userVipLevel->vipLevel->spot_taker_fee_rate,
                'futures_maker_fee_rate' => $userVipLevel->vipLevel->futures_maker_fee_rate,
                'futures_taker_fee_rate' => $userVipLevel->vipLevel->futures_taker_fee_rate,
                'daily_withdrawal_limit' => $userVipLevel->vipLevel->daily_withdrawal_limit,
                'vip_gift' => $userVipLevel->vipLevel->vip_gift,
                'vip_privileges' => $userVipLevel->vipLevel->vip_privileges,
            ]
        ];
    }

    /**
     * 标记用户已领取VIP礼包
     */
    public function markVipGiftReceived(int $userId): bool
    {
        $userVipLevel = UserVipLevel::getUserActiveVipLevel($userId);

        if (!$userVipLevel) {
            throw new BusinessException(ResultCode::FAIL, '用户当前没有VIP等级');
        }

        if ($userVipLevel->gift_received === UserVipLevel::GIFT_RECEIVED_YES) {
            throw new BusinessException(ResultCode::FAIL, '用户已经领取过VIP礼包');
        }

        // 事务
        Db::beginTransaction();
        try {
            $userVipLevel->gift_received = UserVipLevel::GIFT_RECEIVED_YES;
            $userVipLevel->save();

            Db::commit();

            $this->logger->info('用户VIP礼包领取标记成功', [
                'user_id' => $userId,
                'vip_level_id' => $userVipLevel->vip_level_id
            ]);

            return true;
        } catch (\Throwable $th) {
            Db::rollBack();
            $this->logger->error('标记用户VIP礼包领取失败', [
                'user_id' => $userId,
                'error' => $th->getMessage()
            ]);
            throw new BusinessException(ResultCode::FAIL, '标记VIP礼包领取失败，' . $th->getMessage());
        }
    }
}
