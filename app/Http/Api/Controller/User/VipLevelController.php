<?php

declare(strict_types=1);

namespace App\Http\Api\Controller\User;

use App\Http\Api\Request\User\VipLevelRequest;
use App\Http\Api\Service\User\VipLevelService;
use App\Http\Common\Controller\AbstractController;
use App\Http\Common\Result;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\GetMapping;

#[Controller(prefix: 'api/user/vip-level')]
final class VipLevelController extends AbstractController
{
    #[Inject]
    private readonly VipLevelService $vipLevelService;

    /**
     * VIP等级列表
     */
    #[GetMapping('list')]
    public function list(VipLevelRequest $request): Result
    {
        $result = $this->vipLevelService->list($request);
        return $this->success($result);
    }

    /**
     * 获取所有启用的VIP等级（不分页）
     */
    #[GetMapping('all')]
    public function all(): Result
    {
        $result = $this->vipLevelService->getAllActiveVipLevels();
        return $this->success($result);
    }
}
