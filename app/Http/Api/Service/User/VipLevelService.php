<?php

declare(strict_types=1);
/**
 * VIP等级服务
 * Author:chenmaq
 * Contact:tg:@chenmaq
 * Version:1.0
 * Date:2025-01-15
 * Website:bbbtrade.net
 */

namespace App\Http\Api\Service\User;

use App\Http\Api\Service\BaseService;
use App\Model\User\VipLevel;
use App\Model\User\Enums\VipLevelStatus;
use App\QueryBuilder\QueryBuilder;
use Hyperf\HttpServer\Contract\RequestInterface;

class VipLevelService extends BaseService
{
    /**
     * 获取VIP等级列表
     */
    public function list(RequestInterface $request): mixed
    {
        // 构建固定查询条件
        $query = VipLevel::query()->where(VipLevel::FIELD_STATUS, VipLevelStatus::ENABLED);

        return QueryBuilder::for($query, $request)
            ->filters(['name', 'level'])
            ->allowedSorts(['id', 'level', 'sort', 'created_at'])
            ->defaultSort('sort')
            ->pagex();
    }

    /**
     * 根据等级数值获取VIP等级信息
     */
    public function getVipLevelByLevel(int $level): ?array
    {
        $vipLevel = VipLevel::getByLevel($level);

        if (!$vipLevel) {
            return null;
        }

        return [
            'id' => $vipLevel->id,
            'name' => $vipLevel->name,
            'level' => $vipLevel->level,
            'icon' => $vipLevel->icon,
            'color' => $vipLevel->color,
            'spot_trading_volume_requirement' => $vipLevel->spot_trading_volume_requirement,
            'futures_trading_volume_requirement' => $vipLevel->futures_trading_volume_requirement,
            'total_asset_requirement' => $vipLevel->total_asset_requirement,
            'specific_asset_symbol' => $vipLevel->specific_asset_symbol,
            'specific_asset_amount' => $vipLevel->specific_asset_amount,
            'spot_maker_fee_rate' => $vipLevel->spot_maker_fee_rate,
            'spot_taker_fee_rate' => $vipLevel->spot_taker_fee_rate,
            'futures_maker_fee_rate' => $vipLevel->futures_maker_fee_rate,
            'futures_taker_fee_rate' => $vipLevel->futures_taker_fee_rate,
            'daily_withdrawal_limit' => $vipLevel->daily_withdrawal_limit,
            'vip_gift' => $vipLevel->vip_gift,
            'vip_privileges' => $vipLevel->vip_privileges,
            'description' => $vipLevel->description,
            'sort' => $vipLevel->sort,
        ];
    }
}
