<?php

declare(strict_types=1);
/**
 * VIP等级服务
 * Author:chenmaq
 * Contact:tg:@chenmaq
 * Version:1.0
 * Date:2025-01-15
 * Website:bbbtrade.net
 */

namespace App\Http\Api\Service\User;

use App\Http\Api\Service\BaseService;
use App\Model\User\VipLevel;
use App\Model\User\Enums\VipLevelStatus;
use App\QueryBuilder\QueryBuilder;
use Hyperf\HttpServer\Contract\RequestInterface;

class VipLevelService extends BaseService
{
    /**
     * 获取VIP等级列表
     */
    public function list(RequestInterface $request): mixed
    {
        return QueryBuilder::for(VipLevel::class, $request)
            ->filters(['name', 'level', 'status'])
            ->allowedSorts(['id', 'level', 'sort', 'created_at'])
            ->defaultSort('sort')
            ->where(VipLevel::FIELD_STATUS, VipLevelStatus::ENABLED)
            ->pagex();
    }

    /**
     * 获取所有启用的VIP等级列表（不分页）
     */
    public function getAllActiveVipLevels(): array
    {
        $vipLevels = VipLevel::getActiveVipLevels();
        
        return $vipLevels->map(function (VipLevel $vipLevel) {
            return [
                'id' => $vipLevel->getId(),
                'name' => $vipLevel->getName(),
                'level' => $vipLevel->getLevel(),
                'icon' => $vipLevel->getIcon(),
                'color' => $vipLevel->getColor(),
                'spot_trading_volume_requirement' => $vipLevel->getSpotTradingVolumeRequirement(),
                'futures_trading_volume_requirement' => $vipLevel->getFuturesTradingVolumeRequirement(),
                'total_asset_requirement' => $vipLevel->getTotalAssetRequirement(),
                'specific_asset_symbol' => $vipLevel->getSpecificAssetSymbol(),
                'specific_asset_amount' => $vipLevel->getSpecificAssetAmount(),
                'spot_maker_fee_rate' => $vipLevel->getSpotMakerFeeRate(),
                'spot_taker_fee_rate' => $vipLevel->getSpotTakerFeeRate(),
                'futures_maker_fee_rate' => $vipLevel->getFuturesMakerFeeRate(),
                'futures_taker_fee_rate' => $vipLevel->getFuturesTakerFeeRate(),
                'daily_withdrawal_limit' => $vipLevel->getDailyWithdrawalLimit(),
                'vip_gift' => $vipLevel->getVipGift(),
                'vip_privileges' => $vipLevel->getVipPrivileges(),
                'description' => $vipLevel->getDescription(),
                'sort' => $vipLevel->getSort(),
            ];
        })->toArray();
    }

    /**
     * 根据等级数值获取VIP等级信息
     */
    public function getVipLevelByLevel(int $level): ?array
    {
        $vipLevel = VipLevel::getByLevel($level);
        
        if (!$vipLevel) {
            return null;
        }

        return [
            'id' => $vipLevel->getId(),
            'name' => $vipLevel->getName(),
            'level' => $vipLevel->getLevel(),
            'icon' => $vipLevel->getIcon(),
            'color' => $vipLevel->getColor(),
            'spot_trading_volume_requirement' => $vipLevel->getSpotTradingVolumeRequirement(),
            'futures_trading_volume_requirement' => $vipLevel->getFuturesTradingVolumeRequirement(),
            'total_asset_requirement' => $vipLevel->getTotalAssetRequirement(),
            'specific_asset_symbol' => $vipLevel->getSpecificAssetSymbol(),
            'specific_asset_amount' => $vipLevel->getSpecificAssetAmount(),
            'spot_maker_fee_rate' => $vipLevel->getSpotMakerFeeRate(),
            'spot_taker_fee_rate' => $vipLevel->getSpotTakerFeeRate(),
            'futures_maker_fee_rate' => $vipLevel->getFuturesMakerFeeRate(),
            'futures_taker_fee_rate' => $vipLevel->getFuturesTakerFeeRate(),
            'daily_withdrawal_limit' => $vipLevel->getDailyWithdrawalLimit(),
            'vip_gift' => $vipLevel->getVipGift(),
            'vip_privileges' => $vipLevel->getVipPrivileges(),
            'description' => $vipLevel->getDescription(),
            'sort' => $vipLevel->getSort(),
        ];
    }
}
